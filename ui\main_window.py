import sys
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from qfluentwidgets import (
    FluentWindow,
    NavigationItemPosition,
    InfoBar,
    InfoBarPosition,
)
from qfluentwidgets import FluentIcon as FIF

from ui.home_page import HomePage
from ui.camera_page import CameraPage
from ui.detection_page import DetectionPage
from ui.config_page import ConfigPage
from ui.log_page import LogPage

from camera.camera_manager import CameraManager
from plc.plc_handler import PlcHandler
from config.config_manager import ConfigManager
from detector.manager import DetectorManager
from data.manager import DataManager
from log.logger import Logger
from core.context import AppContext


class MainWindow(FluentWindow):
    """主窗口类，使用FluentWindow作为主窗口"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("工业视觉缺陷检测系统")

        self._init_managers()

        self._init_ui()

        self._connect_signals()

    def _init_managers(self):
        """初始化各个管理器"""
        self.config_manager = ConfigManager("config/app_config.yaml")
        self.logger = Logger(self.config_manager)
        self.camera_manager = CameraManager()
        self.plc_handler = PlcHandler()
        self.detector_manager = DetectorManager()
        self.data_manager = DataManager()
        self.ctx = AppContext(
            config_manager=self.config_manager,
            logger=self.logger,
            camera_manager=self.camera_manager,
            plc_handler=self.plc_handler,
            detector_manager=self.detector_manager,
            data_manager=self.data_manager,
        )
        self.logger.info("系统管理器初始化完成")

    def _init_ui(self):
        """初始化UI界面"""
        self.home_page = HomePage(self.ctx)
        self.camera_page = CameraPage(self.ctx)
        self.detection_page = DetectionPage(self.ctx)
        self.config_page = ConfigPage(self.ctx)
        self.log_page = LogPage(self.ctx)

        self.addSubInterface(self.home_page, FIF.HOME, "首页")
        self.addSubInterface(self.camera_page, FIF.VIDEO, "相机预览")
        self.addSubInterface(self.detection_page, FIF.FILTER, "检测结果")
        self.addSubInterface(self.config_page, FIF.SETTING, "配置")
        self.addSubInterface(
            self.log_page, FIF.BOOK_SHELF, "日志", NavigationItemPosition.BOTTOM
        )

        # 应用已保存的 PLC 配置到设备（若需要，可在此处自动下发）
        try:
            cm = self.ctx.config_manager
            plc = self.ctx.plc_handler
            if cm and plc:
                blow = cm.get("plc.blow_time_ms")
                if blow is not None:
                    plc.write_blow_time(int(blow))
                trig = cm.get("plc.trigger_interval_ms")
                if trig is not None:
                    plc.write_trigger_interval(int(trig))
                velo = cm.get("plc.run_velocity")
                if velo is not None:
                    plc.write_run_velocity(int(velo))
        except Exception:
            pass

        self.switchTo(self.home_page)

    def _connect_signals(self):
        """连接信号槽"""
        pass

    def show_success_info(self, title: str, content: str):
        """显示成功信息"""
        InfoBar.success(
            title=title,
            content=content,
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP_RIGHT,
            duration=2000,
            parent=self,
        )

    def show_error_info(self, title: str, content: str):
        """显示错误信息"""
        InfoBar.error(
            title=title,
            content=content,
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP_RIGHT,
            duration=2000,
            parent=self,
        )

    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            if hasattr(self, "ctx") and self.ctx:
                self.ctx.shutdown()
        except Exception as e:
            print(f"关闭系统时出错: {e}")
        finally:
            event.accept()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())
