from PySide6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QFormLayout,
    QLabel,
    QFrame,
    QGroupBox,
)
from PySide6.QtCore import Qt
from qfluentwidgets import (
    ScrollArea,
    BodyLabel,
    TitleLabel,
    CardWidget,
    PushButton,
    LineEdit,
    ComboBox,
    SpinBox,
    CheckBox,
)
from ui.base_page import BasePage


class ConfigPage(BasePage):
    """配置页面"""

    def __init__(self, ctx, parent=None):
        super().__init__(ctx, parent=parent)
        self.setObjectName("ConfigPage")
        self.setup_ui()

    def setup_ui(self):
        """设置UI界面"""
        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 标题
        title_label = TitleLabel("配置")
        layout.addWidget(title_label)

        # 创建配置区域
        scroll_area = ScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        scroll_layout.setSpacing(15)

        # 相机配置
        camera_config = self.create_camera_config()
        scroll_layout.addWidget(camera_config)

        # 算法配置
        algorithm_config = self.create_algorithm_config()
        scroll_layout.addWidget(algorithm_config)

        # PLC 配置（带参数）
        plc_config = self.create_plc_config()
        scroll_layout.addWidget(plc_config)

        # 通用配置
        general_config = self.create_general_config()
        scroll_layout.addWidget(general_config)

        scroll_area.setWidget(scroll_widget)
        layout.addWidget(scroll_area)

        # 操作按钮
        button_layout = QHBoxLayout()
        save_button = PushButton("保存配置")
        save_button.clicked.connect(self.save_config)
        load_button = PushButton("加载配置")
        load_button.clicked.connect(self.load_config)
        button_layout.addWidget(save_button)
        button_layout.addWidget(load_button)
        button_layout.addStretch(1)
        layout.addLayout(button_layout)

    def create_camera_config(self):
        """创建相机配置区域"""
        group = QGroupBox("相机配置")
        layout = QVBoxLayout(group)

        # 添加控制按钮
        button_layout = QHBoxLayout()
        add_camera_btn = PushButton("添加相机")
        add_camera_btn.clicked.connect(self.add_camera_config)
        refresh_btn = PushButton("刷新配置")
        refresh_btn.clicked.connect(self.refresh_camera_configs)

        button_layout.addWidget(add_camera_btn)
        button_layout.addWidget(refresh_btn)
        button_layout.addStretch(1)
        layout.addLayout(button_layout)

        # 相机配置容器
        self.camera_config_layout = QVBoxLayout()
        layout.addLayout(self.camera_config_layout)

        # 加载现有配置
        self.load_camera_configs()

        return group

    def create_plc_config(self):
        """创建 PLC 配置区域（参数写入）"""
        group = QGroupBox("PLC 配置")
        layout = QVBoxLayout(group)

        card = CardWidget()
        form = QFormLayout(card)
        form.setLabelAlignment(Qt.AlignRight)
        form.setFieldGrowthPolicy(QFormLayout.AllNonFixedFieldsGrow)

        self.sb_blow_time = SpinBox()
        self.sb_blow_time.setRange(0, 10000)
        self.sb_blow_time.setValue(200)
        form.addRow("吹气时长(ms):", self.sb_blow_time)

        self.sb_trigger_interval = SpinBox()
        self.sb_trigger_interval.setRange(0, 5000)
        self.sb_trigger_interval.setValue(100)
        form.addRow("触发间隔(ms):", self.sb_trigger_interval)

        self.sb_velocity = SpinBox()
        self.sb_velocity.setRange(0, 30000)
        self.sb_velocity.setValue(1000)
        form.addRow("运行速度:", self.sb_velocity)

        row = QHBoxLayout()
        btn_write_blow = PushButton("写入吹气时长")
        btn_write_interval = PushButton("写入触发间隔")
        btn_write_velocity = PushButton("写入运行速度")

        btn_write_blow.clicked.connect(self.on_write_blow_time)
        btn_write_interval.clicked.connect(self.on_write_trigger_interval)
        btn_write_velocity.clicked.connect(self.on_write_velocity)

        row.addWidget(btn_write_blow)
        row.addWidget(btn_write_interval)
        row.addWidget(btn_write_velocity)

        layout.addWidget(card)
        layout.addLayout(row)
        return group

    def create_camera_card(self, config=None):
        """创建单个相机配置卡片"""
        card = CardWidget()
        layout = QFormLayout(card)
        layout.setLabelAlignment(Qt.AlignRight)
        layout.setFieldGrowthPolicy(QFormLayout.AllNonFixedFieldsGrow)

        # 获取配置信息
        if config is None:
            config = {
                "id": f"CAMERA_{len(self.get_camera_cards()) + 1:03d}",
                "name": f"COD{len(self.get_camera_cards()) + 1}",
                "serial_number": "",
                "enabled": True,
                "exposure_time": 10000,
                "gain": 10.0
            }

        # 相机标题和删除按钮
        title_layout = QHBoxLayout()
        title = BodyLabel(config.get("name", "新相机"))
        delete_btn = PushButton("删除")
        delete_btn.clicked.connect(lambda: self.delete_camera_config(card))
        title_layout.addWidget(title)
        title_layout.addStretch(1)
        title_layout.addWidget(delete_btn)

        title_widget = QWidget()
        title_widget.setLayout(title_layout)
        layout.addRow(title_widget, None)

        # ID
        id_edit = LineEdit()
        id_edit.setText(config.get("id", ""))
        id_edit.setObjectName("id_edit")
        layout.addRow("ID:", id_edit)

        # 名称
        name_edit = LineEdit()
        name_edit.setText(config.get("name", ""))
        name_edit.setObjectName("name_edit")
        name_edit.textChanged.connect(lambda text: title.setText(text or "新相机"))
        layout.addRow("名称:", name_edit)

        # 序列号
        serial_edit = LineEdit()
        serial_edit.setText(config.get("serial_number", ""))
        serial_edit.setObjectName("serial_edit")
        layout.addRow("序列号:", serial_edit)

        # 启用选项
        enabled_checkbox = CheckBox("启用")
        enabled_checkbox.setChecked(config.get("enabled", True))
        enabled_checkbox.setObjectName("enabled_checkbox")
        layout.addRow("启用:", enabled_checkbox)

        # 曝光时间
        exposure_spin = SpinBox()
        exposure_spin.setRange(100, 100000)
        exposure_spin.setValue(config.get("exposure_time", 10000))
        exposure_spin.setObjectName("exposure_spin")
        layout.addRow("曝光时间:", exposure_spin)

        # 增益
        gain_spin = SpinBox()
        gain_spin.setRange(0, 20)
        gain_spin.setValue(config.get("gain", 10.0))
        gain_spin.setObjectName("gain_spin")
        layout.addRow("增益:", gain_spin)

        # 分辨率
        resolution_combo = ComboBox()
        resolution_combo.addItem("1920x1080")
        resolution_combo.addItem("1280x720")
        resolution_combo.addItem("640x480")
        layout.addRow("分辨率:", resolution_combo)

        # 触发模式
        trigger_combo = ComboBox()
        trigger_combo.addItem("连续模式")
        trigger_combo.addItem("触发模式")
        layout.addRow("触发模式:", trigger_combo)

        # 存储原始配置信息
        card.original_config = config

        return card

    def get_camera_cards(self):
        """获取所有相机配置卡片"""
        cards = []
        for i in range(self.camera_config_layout.count()):
            item = self.camera_config_layout.itemAt(i)
            if item and item.widget():
                cards.append(item.widget())
        return cards

    def load_camera_configs(self):
        """加载相机配置"""
        # 清空现有配置
        self.clear_camera_configs()

        # 从配置管理器获取相机配置
        if self.ctx and self.ctx.camera_manager:
            configs = self.ctx.camera_manager.get_camera_configs()
            for config in configs:
                card = self.create_camera_card(config)
                self.camera_config_layout.addWidget(card)

    def clear_camera_configs(self):
        """清空相机配置"""
        while self.camera_config_layout.count():
            child = self.camera_config_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

    def add_camera_config(self):
        """添加新的相机配置"""
        card = self.create_camera_card()
        self.camera_config_layout.addWidget(card)

    def delete_camera_config(self, card):
        """删除相机配置"""
        # 从布局中移除
        self.camera_config_layout.removeWidget(card)
        card.deleteLater()

    def refresh_camera_configs(self):
        """刷新相机配置"""
        self.load_camera_configs()

    def get_camera_config_from_card(self, card):
        """从卡片获取相机配置"""
        layout = card.layout()
        config = {}

        # 遍历表单布局获取控件值
        for i in range(layout.rowCount()):
            label_item = layout.itemAt(i, QFormLayout.LabelRole)
            field_item = layout.itemAt(i, QFormLayout.FieldRole)

            if label_item and field_item and field_item.widget():
                widget = field_item.widget()

                if hasattr(widget, 'objectName'):
                    name = widget.objectName()
                    if name == "id_edit":
                        config["id"] = widget.text()
                    elif name == "name_edit":
                        config["name"] = widget.text()
                    elif name == "serial_edit":
                        config["serial_number"] = widget.text()
                    elif name == "enabled_checkbox":
                        config["enabled"] = widget.isChecked()
                    elif name == "exposure_spin":
                        config["exposure_time"] = widget.value()
                    elif name == "gain_spin":
                        config["gain"] = widget.value()

        return config

    def create_algorithm_config(self):
        """创建算法配置区域"""
        group = QGroupBox("算法配置")
        layout = QVBoxLayout(group)

        # 算法配置卡片
        card = CardWidget()
        form_layout = QFormLayout(card)
        form_layout.setLabelAlignment(Qt.AlignRight)
        form_layout.setFieldGrowthPolicy(QFormLayout.AllNonFixedFieldsGrow)

        # 模型路径
        model_path_edit = LineEdit()
        model_path_edit.setText("models/yolov8_defect.pt")
        form_layout.addRow("模型路径:", model_path_edit)

        # 类别文件
        class_file_edit = LineEdit()
        class_file_edit.setText("models/classes.txt")
        form_layout.addRow("类别文件:", class_file_edit)

        # 置信度阈值
        confidence_spin = SpinBox()
        confidence_spin.setRange(0, 100)
        confidence_spin.setValue(50)
        confidence_spin.setSuffix(" %")
        form_layout.addRow("置信度阈值:", confidence_spin)

        layout.addWidget(card)
        return group

    def create_general_config(self):
        """创建通用配置区域"""
        group = QGroupBox("通用配置")
        layout = QVBoxLayout(group)

        # 通用配置卡片
        card = CardWidget()
        form_layout = QFormLayout(card)
        form_layout.setLabelAlignment(Qt.AlignRight)
        form_layout.setFieldGrowthPolicy(QFormLayout.AllNonFixedFieldsGrow)

        # 保存路径
        save_path_edit = LineEdit()
        save_path_edit.setText("./results")
        form_layout.addRow("保存路径:", save_path_edit)

        # 文件格式
        format_combo = ComboBox()
        format_combo.addItem("JPEG")
        format_combo.addItem("PNG")
        format_combo.addItem("BMP")
        form_layout.addRow("文件格式:", format_combo)

        # 日志级别
        log_level_combo = ComboBox()
        log_level_combo.addItem("DEBUG")
        log_level_combo.addItem("INFO")
        log_level_combo.addItem("WARNING")
        log_level_combo.addItem("ERROR")
        log_level_combo.setCurrentText("INFO")
        form_layout.addRow("日志级别:", log_level_combo)

        # 线程数
        thread_spin = SpinBox()
        thread_spin.setRange(1, 16)
        thread_spin.setValue(4)
        form_layout.addRow("线程数:", thread_spin)

        layout.addWidget(card)
        return group

    def save_config(self):
        """保存配置"""
        try:
            # 保存相机配置
            if self.ctx and self.ctx.camera_manager:
                # 收集所有相机配置
                camera_configs = []
                for card in self.get_camera_cards():
                    config = self.get_camera_config_from_card(card)
                    if config.get("serial_number"):  # 只保存有序列号的配置
                        camera_configs.append(config)

                # 更新相机管理器的配置
                self.ctx.camera_manager.camera_configs = camera_configs

                # 保存到配置文件
                if self.ctx.config_manager:
                    self.ctx.config_manager.set("camera.devices", camera_configs)

            # 持久化 PLC 配置
            if self.ctx and getattr(self.ctx, "config_manager", None):
                cm = self.ctx.config_manager
                cm.set("plc.blow_time_ms", int(self.sb_blow_time.value()))
                cm.set("plc.trigger_interval_ms", int(self.sb_trigger_interval.value()))
                cm.set("plc.run_velocity", int(self.sb_velocity.value()))
                cm.save()
            # 立即下发到 PLC
            if self.ctx and getattr(self.ctx, "plc_handler", None):
                plc = self.ctx.plc_handler
                try:
                    plc.write_blow_time(int(self.sb_blow_time.value()))
                except Exception:
                    pass
                try:
                    plc.write_trigger_interval(int(self.sb_trigger_interval.value()))
                except Exception:
                    pass
                try:
                    plc.write_run_velocity(int(self.sb_velocity.value()))
                except Exception:
                    pass
            if self.parent and hasattr(self.parent, "show_success_info"):
                self.parent.show_success_info("配置", "配置已保存")
            else:
                print("配置保存成功")
        except Exception as e:
            print(f"保存配置失败: {e}")

    def load_config(self):
        """加载配置"""
        try:
            # 重新加载配置文件
            if self.ctx and getattr(self.ctx, "config_manager", None):
                cm = self.ctx.config_manager
                cm.reload()

                # 重新加载相机管理器配置
                if self.ctx.camera_manager:
                    self.ctx.camera_manager.reload_configs()

                # 刷新相机配置UI
                self.load_camera_configs()

                # 加载PLC配置
                blow = cm.get("plc.blow_time_ms", int(self.sb_blow_time.value()))
                trig = cm.get(
                    "plc.trigger_interval_ms", int(self.sb_trigger_interval.value())
                )
                velo = cm.get("plc.run_velocity", int(self.sb_velocity.value()))
                self.sb_blow_time.setValue(int(blow))
                self.sb_trigger_interval.setValue(int(trig))
                self.sb_velocity.setValue(int(velo))

            if self.parent and hasattr(self.parent, "show_success_info"):
                self.parent.show_success_info("配置", "配置已加载")
            else:
                print("配置加载成功")
        except Exception as e:
            print(f"加载配置失败: {e}")

    # PLC 配置写入槽
    def on_write_blow_time(self):
        try:
            if self.ctx and getattr(self.ctx, "plc_handler", None):
                self.ctx.plc_handler.write_blow_time(int(self.sb_blow_time.value()))
        except Exception as e:
            print(f"写入吹气时长失败: {e}")

    def on_write_trigger_interval(self):
        try:
            if self.ctx and getattr(self.ctx, "plc_handler", None):
                self.ctx.plc_handler.write_trigger_interval(
                    int(self.sb_trigger_interval.value())
                )
        except Exception as e:
            print(f"写入触发间隔失败: {e}")

    def on_write_velocity(self):
        try:
            if self.ctx and getattr(self.ctx, "plc_handler", None):
                self.ctx.plc_handler.write_run_velocity(int(self.sb_velocity.value()))
        except Exception as e:
            print(f"写入运行速度失败: {e}")
