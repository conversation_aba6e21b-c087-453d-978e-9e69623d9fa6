from PySide6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QGridLayout,
    QLabel,
    QPushButton,
    QFrame,
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QPixmap, QImage
from qfluentwidgets import (
    ScrollArea,
    BodyLabel,
    TitleLabel,
    CardWidget,
    PushButton,
    Slider,
    ComboBox,
)
import cv2
import numpy as np
import ctypes
from camera.PixelType_header import PixelType_Gvsp_BGR8_Packed
from ui.base_page import BasePage


class CameraPage(BasePage):
    """相机预览页面"""

    def __init__(self, ctx, parent=None):
        super().__init__(ctx, parent=parent)
        self.setObjectName("CameraPage")
        self.camera_frames = {}
        self.setup_ui()
        self.initialize_cameras_soft_trigger()
        self.capture_and_display_all()

    def setup_ui(self):
        """设置UI界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        title_label = TitleLabel("相机预览")
        layout.addWidget(title_label)

        self.camera_grid = QGridLayout()
        self.camera_grid.setSpacing(15)
        layout.addLayout(self.camera_grid)

        self.init_camera_views()

        control_panel = self.create_control_panel()
        layout.addWidget(control_panel)

        layout.addStretch(1)

    def init_camera_views(self):
        """初始化相机显示视图"""
        # 根据配置的相机数量创建显示区域
        if self.ctx and self.ctx.camera_manager:
            configs = self.ctx.camera_manager.get_camera_configs()
            enabled_configs = [config for config in configs if config.get("enabled", True)]

            for i, config in enumerate(enabled_configs):
                camera_widget = self.create_camera_widget(config, i)
                row = i // 3
                col = i % 3
                self.camera_grid.addWidget(camera_widget, row, col)
        else:
            # 如果没有配置管理器，创建默认的6个相机显示区域
            for i in range(6):
                default_config = {
                    "name": f"COD{i+1}",
                    "serial_number": "",
                    "id": f"CAMERA_{i+1:03d}"
                }
                camera_widget = self.create_camera_widget(default_config, i)
                row = i // 3
                col = i % 3
                self.camera_grid.addWidget(camera_widget, row, col)

    def create_camera_widget(self, config, camera_index):
        """创建单个相机显示控件"""
        card = CardWidget()
        card.setMinimumHeight(200)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(10, 10, 10, 10)

        # 使用配置中的名称
        camera_name = config.get("name", f"相机 {camera_index + 1}")
        title = BodyLabel(camera_name)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # 显示序列号（如果有）
        serial_number = config.get("serial_number", "")
        if serial_number:
            serial_label = BodyLabel(f"SN: {serial_number}")
            serial_label.setAlignment(Qt.AlignCenter)
            serial_label.setStyleSheet("color: #666; font-size: 10px;")
            layout.addWidget(serial_label)

        image_label = QLabel()
        image_label.setMinimumSize(200, 150)
        image_label.setAlignment(Qt.AlignCenter)
        image_label.setStyleSheet("background-color: #f0f0f0; border: 1px solid #ccc;")
        image_label.setText("无图像")
        layout.addWidget(image_label)

        # 存储配置信息
        card.image_label = image_label
        card.camera_id = camera_index
        card.camera_config = config
        card.camera_name = camera_name
        card.serial_number = serial_number

        card.mousePressEvent = lambda event, cid=camera_index: self.on_camera_click(cid)

        return card

    def create_control_panel(self):
        """创建控制面板"""
        panel = CardWidget()
        layout = QHBoxLayout(panel)
        layout.setContentsMargins(15, 15, 15, 15)

        camera_label = BodyLabel("相机:")
        self.camera_combo = ComboBox()
        self.update_camera_combo()

        exposure_label = BodyLabel("曝光:")
        self.exposure_slider = Slider(Qt.Horizontal)
        self.exposure_slider.setRange(100, 100000)
        self.exposure_slider.setValue(10000)

        gain_label = BodyLabel("增益:")
        self.gain_slider = Slider(Qt.Horizontal)
        self.gain_slider.setRange(0, 20)
        self.gain_slider.setValue(10)

        save_button = PushButton("保存图像")
        save_button.clicked.connect(self.save_current_image)
        refresh_button = PushButton("抓拍刷新")
        refresh_button.clicked.connect(self.capture_and_display_all)

        layout.addWidget(camera_label)
        layout.addWidget(self.camera_combo)
        layout.addWidget(exposure_label)
        layout.addWidget(self.exposure_slider)
        layout.addWidget(gain_label)
        layout.addWidget(self.gain_slider)
        layout.addWidget(save_button)
        layout.addWidget(refresh_button)

        return panel

    def update_camera_combo(self):
        """更新相机下拉选择框"""
        self.camera_combo.clear()

        if self.ctx and self.ctx.camera_manager:
            configs = self.ctx.camera_manager.get_camera_configs()
            enabled_configs = [config for config in configs if config.get("enabled", True)]

            for i, config in enumerate(enabled_configs):
                camera_name = config.get("name", f"相机 {i + 1}")
                self.camera_combo.addItem(camera_name)
        else:
            # 默认添加6个相机
            for i in range(6):
                self.camera_combo.addItem(f"COD{i + 1}")

    def get_camera_widgets(self):
        """获取所有相机显示控件"""
        widgets = []
        for i in range(self.camera_grid.count()):
            item = self.camera_grid.itemAt(i)
            if item and item.widget():
                widgets.append(item.widget())
        return widgets

    def refresh_camera_display(self):
        """刷新相机显示"""
        # 清空现有显示
        while self.camera_grid.count():
            child = self.camera_grid.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        # 重新初始化相机视图
        self.init_camera_views()

        # 更新下拉选择框
        self.update_camera_combo()

    def initialize_cameras_soft_trigger(self):
        """启动时将配置中指定的相机设置为软触发并开始取流"""
        try:
            cm = self.ctx.camera_manager
            ret = cm.enum_devices()
            if ret != 0:
                return

            # 只初始化配置中指定的相机
            configured_devices = cm.get_configured_devices()
            for device in configured_devices:
                try:
                    # 通过序列号打开设备
                    if cm.open_device(device.serial_number) != 0:
                        print(f"无法打开相机 {device.serial_number}")
                        continue

                    # 设置触发模式为软触发
                    cm.set_trigger_mode(device.serial_number, True)

                    # 应用配置中的参数
                    if hasattr(device, 'config_exposure_time'):
                        try:
                            cm.set_float_value(device.serial_number, "ExposureTime", device.config_exposure_time)
                        except Exception:
                            pass

                    if hasattr(device, 'config_gain'):
                        try:
                            cm.set_float_value(device.serial_number, "Gain", device.config_gain)
                        except Exception:
                            pass

                    # 建议关闭自动曝光和自动增益以稳定图像（若支持）
                    try:
                        cm.set_bool_value(device.serial_number, "ExposureAuto", False)
                        cm.set_bool_value(device.serial_number, "GainAuto", False)
                    except Exception:
                        pass

                    # 像素格式为 BGR8 以便直接渲染
                    try:
                        cm.set_enum_value(device.serial_number, "PixelFormat", PixelType_Gvsp_BGR8_Packed)
                    except Exception:
                        pass

                    # 开始取流
                    cm.start_grabbing(device.serial_number)
                    print(f"相机 {device.config_name} ({device.serial_number}) 初始化成功")

                except Exception as e:
                    print(f"初始化相机 {device.serial_number} 失败: {e}")

        except Exception as e:
            print(f"初始化软触发相机失败: {e}")

    def capture_and_display_all(self):
        """对配置中指定的相机执行一次软触发并渲染当前帧"""
        try:
            cm = self.ctx.camera_manager
            configured_devices = cm.get_configured_devices()

            for idx, device in enumerate(configured_devices):
                try:
                    # 触发后等待一帧
                    cm.trigger_software(device.serial_number)
                    frame = cm.get_frame_bgr(device.serial_number, timeout=1500) or cm.get_frame(device.serial_number, timeout=1500)
                    if frame:
                        img = self._frame_to_bgr_image(frame)
                        if img is not None:
                            self.update_camera_display(idx, img)
                except Exception as e:
                    print(f"相机 {device.serial_number} 抓拍失败: {e}")
                    continue
        except Exception as e:
            print(f"抓拍渲染失败: {e}")

    def _frame_to_bgr_image(self, frame):
        """将底层帧数据转换为 BGR ndarray"""
        width = frame.get("width", 0)
        height = frame.get("height", 0)
        frame_len = frame.get("frame_len", 0)
        data_bytes = frame.get("data")
        if not data_bytes or width <= 0 or height <= 0:
            return None
        expected_len = width * height * 3
        buf = data_bytes if frame_len >= expected_len else data_bytes[:expected_len]
        try:
            img = np.frombuffer(buf, dtype=np.uint8).reshape((height, width, 3))
            return img
        except Exception:
            return None

    def update_camera_display(self, camera_id, frame):
        """更新相机显示"""
        try:
            camera_widget = None
            for i in range(self.camera_grid.count()):
                widget = self.camera_grid.itemAt(i).widget()
                if (
                    widget
                    and hasattr(widget, "camera_id")
                    and widget.camera_id == camera_id
                ):
                    camera_widget = widget
                    break

            if camera_widget and hasattr(camera_widget, "image_label"):
                rgb_image = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                h, w, ch = rgb_image.shape
                bytes_per_line = ch * w
                qt_image = QImage(
                    rgb_image.data, w, h, bytes_per_line, QImage.Format_RGB888
                )

                pixmap = QPixmap.fromImage(qt_image)
                camera_widget.image_label.setPixmap(
                    pixmap.scaled(
                        camera_widget.image_label.width(),
                        camera_widget.image_label.height(),
                        Qt.KeepAspectRatio,
                        Qt.SmoothTransformation,
                    )
                )
        except Exception as e:
            print(f"更新相机显示时出错: {e}")

    def on_camera_click(self, camera_id):
        """相机点击事件"""
        print(f"相机 {camera_id + 1} 被点击")

    def save_current_image(self):
        """保存当前图像"""
        print("保存图像功能待实现")
