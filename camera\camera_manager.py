import time
from typing import List, Optional, Dict, Any
from ctypes import c_bool, POINTER, byref, sizeof, cast, memset
import ctypes

from CameraParams_const import *
from MvCameraControl_class import MvCamera
from CameraParams_header import *
from MvErrorDefine_const import *
from PixelType_header import *


class CameraDevice:
    """相机设备类"""

    def __init__(self, device_info: MV_CC_DEVICE_INFO, device_index: int):
        self.device_info = device_info
        self.device_index = device_index
        self.camera = MvCamera()
        self.is_opened = False
        self.is_grabbing = False
        self.frame_callback = None
        self.callback_user_data = None

        # 设备基本信息
        self.serial_number = ""
        self.model_name = ""
        self._parse_device_info()

    def _parse_device_info(self):
        """解析设备信息"""
        if (
            self.device_info.nTLayerType == MV_GIGE_DEVICE
            or self.device_info.nTLayerType == MV_GENTL_GIGE_DEVICE
        ):
            self.model_name = self._decode_char(
                self.device_info.SpecialInfo.stGigEInfo.chModelName
            )
            for per in self.device_info.SpecialInfo.stGigEInfo.chSerialNumber:
                if per == 0:
                    break
                self.serial_number = self.serial_number + chr(per)
        elif self.device_info.nTLayerType == MV_USB_DEVICE:
            self.model_name = self._decode_char(
                self.device_info.SpecialInfo.stUsb3VInfo.chModelName
            )
            for per in self.device_info.SpecialInfo.stUsb3VInfo.chSerialNumber:
                if per == 0:
                    break
                self.serial_number = self.serial_number + chr(per)

    def _decode_char(self, char_array):
        """解码字符数组"""
        # 使用ctypes.cast将c_ubyte数组转换为c_char_p指针
        c_char_p_value = ctypes.cast(char_array, ctypes.c_char_p)
        try:
            decode_str = c_char_p_value.value.decode("gbk")  # 中文字符
        except UnicodeDecodeError:
            decode_str = str(c_char_p_value.value)
        return decode_str

    def open(self) -> int:
        """打开相机"""
        if self.is_opened:
            return MV_OK

        # 创建句柄
        ret = self.camera.MV_CC_CreateHandle(self.device_info)
        if ret != MV_OK:
            return ret

        # 打开设备
        ret = self.camera.MV_CC_OpenDevice()
        if ret != MV_OK:
            self.camera.MV_CC_DestroyHandle()
            return ret

        self.is_opened = True

        # 探测网络最佳包大小(只对GigE相机有效)
        if (
            self.device_info.nTLayerType == MV_GIGE_DEVICE
            or self.device_info.nTLayerType == MV_GENTL_GIGE_DEVICE
        ):
            nPacketSize = self.camera.MV_CC_GetOptimalPacketSize()
            if int(nPacketSize) > 0:
                ret = self.camera.MV_CC_SetIntValue("GevSCPSPacketSize", nPacketSize)
                if ret != MV_OK:
                    print(f"Warning: set packet size fail! ret[0x{ret:x}]")

        return MV_OK

    def close(self) -> int:
        """关闭相机"""
        if not self.is_opened:
            return MV_OK

        # 停止取流
        if self.is_grabbing:
            self.stop_grabbing()

        # 关闭设备
        ret = self.camera.MV_CC_CloseDevice()
        if ret != MV_OK:
            return ret

        # 销毁句柄
        self.camera.MV_CC_DestroyHandle()
        self.is_opened = False
        return MV_OK

    def start_grabbing(self) -> int:
        """开始取流"""
        if not self.is_opened:
            return MV_E_CALLORDER

        if self.is_grabbing:
            return MV_OK

        ret = self.camera.MV_CC_StartGrabbing()
        if ret != MV_OK:
            return ret

        self.is_grabbing = True
        return MV_OK

    def stop_grabbing(self) -> int:
        """停止取流"""
        if not self.is_opened:
            return MV_E_CALLORDER

        if not self.is_grabbing:
            return MV_OK

        ret = self.camera.MV_CC_StopGrabbing()
        if ret != MV_OK:
            return ret

        self.is_grabbing = False
        return MV_OK

    def set_trigger_mode(self, is_trigger_mode: bool) -> int:
        """设置触发模式"""
        if not self.is_opened:
            return MV_E_CALLORDER

        if not is_trigger_mode:
            ret = self.camera.MV_CC_SetEnumValue("TriggerMode", MV_TRIGGER_MODE_OFF)
        else:
            # 设置触发模式为 ON，并指定触发源为软件
            ret = self.camera.MV_CC_SetEnumValue("TriggerMode", MV_TRIGGER_MODE_ON)
            if ret == MV_OK:
                try:
                    self.camera.MV_CC_SetEnumValue(
                        "TriggerSource", MV_TRIGGER_SOURCE_SOFTWARE
                    )
                except Exception:
                    pass

        return ret

    def trigger_software(self) -> int:
        """软件触发一次"""
        if not self.is_opened:
            return MV_E_CALLORDER

        return self.camera.MV_CC_SetCommandValue("TriggerSoftware")

    def get_frame(self, timeout: int = 1000) -> Optional[Dict[str, Any]]:
        """获取一帧图像"""
        if not self.is_opened or not self.is_grabbing:
            return None

        stOutFrame = MV_FRAME_OUT()
        memset(byref(stOutFrame), 0, sizeof(stOutFrame))

        ret = self.camera.MV_CC_GetImageBuffer(stOutFrame, timeout)
        if ret == MV_OK:
            # 在释放缓存前拷贝数据到bytes
            frame_len = getattr(stOutFrame.stFrameInfo, "nFrameLen", 0)
            try:
                buf_bytes = ctypes.string_at(stOutFrame.pBufAddr, frame_len) if frame_len > 0 else b""
            except Exception:
                buf_bytes = b""

            frame_data = {
                "data": buf_bytes,
                "info": stOutFrame.stFrameInfo,
                "width": stOutFrame.stFrameInfo.nWidth,
                "height": stOutFrame.stFrameInfo.nHeight,
                "pixel_type": stOutFrame.stFrameInfo.enPixelType,
                "frame_num": stOutFrame.stFrameInfo.nFrameNum,
                "timestamp": stOutFrame.stFrameInfo.nHostTimeStamp,
                "frame_len": frame_len,
            }

            # 释放图像缓存
            self.camera.MV_CC_FreeImageBuffer(stOutFrame)
            return frame_data
        else:
            return None

    def get_frame_bgr(self, timeout: int = 1000) -> Optional[Dict[str, Any]]:
        """直接获取转换为BGR8的一帧图像（使用SDK内部转换）。"""
        if not self.is_opened or not self.is_grabbing:
            return None

        # 预估缓冲区大小：使用当前 Width/Height
        try:
            w_info = self.get_int_value("Width")
            h_info = self.get_int_value("Height")
            width = int(w_info["value"]) if w_info else 0
            height = int(h_info["value"]) if h_info else 0
        except Exception:
            width, height = 0, 0

        if width <= 0 or height <= 0:
            return None

        buf_size = width * height * 3
        stFrameInfo = MV_FRAME_OUT_INFO_EX()
        memset(byref(stFrameInfo), 0, sizeof(stFrameInfo))
        # 分配接收缓冲
        recv_buf = (ctypes.c_ubyte * buf_size)()
        ret = self.camera.MV_CC_GetImageForBGR(recv_buf, buf_size, stFrameInfo, timeout)
        if ret == MV_OK:
            try:
                data = ctypes.string_at(recv_buf, buf_size)
            except Exception:
                data = bytes(bytearray(recv_buf))

            return {
                "data": data,
                "width": stFrameInfo.nWidth or width,
                "height": stFrameInfo.nHeight or height,
                "pixel_type": PixelType_Gvsp_BGR8_Packed,
                "frame_num": stFrameInfo.nFrameNum,
                "timestamp": stFrameInfo.nHostTimeStamp,
                "frame_len": buf_size,
            }
        return None

    def get_int_value(self, param_name: str) -> Optional[Dict[str, Any]]:
        """获取整型参数值"""
        if not self.is_opened:
            return None

        stIntValue = MVCC_INTVALUE_EX()
        memset(byref(stIntValue), 0, sizeof(stIntValue))

        ret = self.camera.MV_CC_GetIntValueEx(param_name, stIntValue)
        if ret == MV_OK:
            return {
                "value": stIntValue.nCurValue,
                "min": stIntValue.nMin,
                "max": stIntValue.nMax,
                "inc": stIntValue.nInc,
            }
        else:
            return None

    def set_int_value(self, param_name: str, value: int) -> int:
        """设置整型参数值"""
        if not self.is_opened:
            return MV_E_CALLORDER

        return self.camera.MV_CC_SetIntValueEx(param_name, value)

    def get_float_value(self, param_name: str) -> Optional[Dict[str, Any]]:
        """获取浮点型参数值"""
        if not self.is_opened:
            return None

        stFloatValue = MVCC_FLOATVALUE()
        memset(byref(stFloatValue), 0, sizeof(stFloatValue))

        ret = self.camera.MV_CC_GetFloatValue(param_name, stFloatValue)
        if ret == MV_OK:
            return {
                "value": stFloatValue.fCurValue,
                "min": stFloatValue.fMin,
                "max": stFloatValue.fMax,
            }
        else:
            return None

    def set_float_value(self, param_name: str, value: float) -> int:
        """设置浮点型参数值"""
        if not self.is_opened:
            return MV_E_CALLORDER

        return self.camera.MV_CC_SetFloatValue(param_name, value)

    def get_enum_value(self, param_name: str) -> Optional[Dict[str, Any]]:
        """获取枚举型参数值"""
        if not self.is_opened:
            return None

        stEnumValue = MVCC_ENUMVALUE_EX()
        memset(byref(stEnumValue), 0, sizeof(stEnumValue))

        ret = self.camera.MV_CC_GetEnumValueEx(param_name, stEnumValue)
        if ret == MV_OK:
            # 确保不会访问超出范围的数组元素
            supported_count = min(
                stEnumValue.nSupportedNum, 256
            )  # MVCC_ENUMVALUE_EX支持最多256个值
            return {
                "value": stEnumValue.nCurValue,
                "supported": [
                    stEnumValue.nSupportValue[i] for i in range(supported_count)
                ],
            }
        else:
            return None

    def set_enum_value(self, param_name: str, value: int) -> int:
        """设置枚举型参数值"""
        if not self.is_opened:
            return MV_E_CALLORDER

        return self.camera.MV_CC_SetEnumValue(param_name, value)

    def get_bool_value(self, param_name: str) -> Optional[bool]:
        """获取布尔型参数值"""
        if not self.is_opened:
            return None

        stBoolValue = c_bool()
        ret = self.camera.MV_CC_GetBoolValue(param_name, stBoolValue)
        if ret == MV_OK:
            return stBoolValue.value
        else:
            return None

    def set_bool_value(self, param_name: str, value: bool) -> int:
        """设置布尔型参数值"""
        if not self.is_opened:
            return MV_E_CALLORDER

        return self.camera.MV_CC_SetBoolValue(param_name, value)

    def get_string_value(self, param_name: str) -> Optional[str]:
        """获取字符串型参数值"""
        if not self.is_opened:
            return None

        stStringValue = MVCC_STRINGVALUE()
        memset(byref(stStringValue), 0, sizeof(stStringValue))

        ret = self.camera.MV_CC_GetStringValue(param_name, stStringValue)
        if ret == MV_OK:
            return stStringValue.chCurValue.decode("utf-8").strip("\x00")
        else:
            return None

    def set_string_value(self, param_name: str, value: str) -> int:
        """设置字符串型参数值"""
        if not self.is_opened:
            return MV_E_CALLORDER

        return self.camera.MV_CC_SetStringValue(param_name, value)


class CameraManager:
    """相机管理器类"""

    def __init__(self):
        self.devices: List[CameraDevice] = []
        self.device_map: Dict[str, CameraDevice] = {}  # 通过序列号映射设备
        self.is_initialized = False
        self._initialize()

    def _initialize(self):
        """初始化SDK"""
        ret = MvCamera.MV_CC_Initialize()
        if ret == MV_OK:
            self.is_initialized = True
        return ret

    def __del__(self):
        """析构函数，释放资源"""
        self.close_all()
        if self.is_initialized:
            MvCamera.MV_CC_Finalize()

    def enum_devices(self, vendor_name: str = "") -> int:
        """枚举设备"""
        # 清空现有设备列表
        self.devices.clear()
        self.device_map.clear()

        # 枚举设备
        device_list = MV_CC_DEVICE_INFO_LIST()
        n_layer_type = MV_GIGE_DEVICE | MV_USB_DEVICE

        if vendor_name:
            ret = MvCamera.MV_CC_EnumDevicesEx(n_layer_type, device_list, vendor_name)
        else:
            ret = MvCamera.MV_CC_EnumDevices(n_layer_type, device_list)

        if ret != MV_OK or device_list.nDeviceNum == 0:
            return ret

        # 创建设备对象
        for i in range(device_list.nDeviceNum):
            mvcc_dev_info = cast(
                device_list.pDeviceInfo[i], POINTER(MV_CC_DEVICE_INFO)
            ).contents
            device = CameraDevice(mvcc_dev_info, i)
            self.devices.append(device)
            self.device_map[device.serial_number] = device

        return MV_OK

    def get_device_count(self) -> int:
        """获取设备数量"""
        return len(self.devices)

    def get_device_by_index(self, index: int) -> Optional[CameraDevice]:
        """通过索引获取设备"""
        if 0 <= index < len(self.devices):
            return self.devices[index]
        return None

    def get_device_by_serial(self, serial_number: str) -> Optional[CameraDevice]:
        """通过序列号获取设备"""
        return self.device_map.get(serial_number)

    def open_device(self, device_identifier) -> int:
        """打开设备"""
        device = None
        if isinstance(device_identifier, int):
            device = self.get_device_by_index(device_identifier)
        elif isinstance(device_identifier, str):
            device = self.get_device_by_serial(device_identifier)

        if device is None:
            return MV_E_HANDLE

        return device.open()

    def close_device(self, device_identifier) -> int:
        """关闭设备"""
        device = None
        if isinstance(device_identifier, int):
            device = self.get_device_by_index(device_identifier)
        elif isinstance(device_identifier, str):
            device = self.get_device_by_serial(device_identifier)

        if device is None:
            return MV_E_HANDLE

        return device.close()

    def close_all(self):
        """关闭所有设备"""
        for device in self.devices:
            if device.is_opened:
                device.close()

    def start_grabbing(self, device_identifier) -> int:
        """开始取流"""
        device = None
        if isinstance(device_identifier, int):
            device = self.get_device_by_index(device_identifier)
        elif isinstance(device_identifier, str):
            device = self.get_device_by_serial(device_identifier)

        if device is None:
            return MV_E_HANDLE

        return device.start_grabbing()

    def stop_grabbing(self, device_identifier) -> int:
        """停止取流"""
        device = None
        if isinstance(device_identifier, int):
            device = self.get_device_by_index(device_identifier)
        elif isinstance(device_identifier, str):
            device = self.get_device_by_serial(device_identifier)

        if device is None:
            return MV_E_HANDLE

        return device.stop_grabbing()

    def set_trigger_mode(self, device_identifier, is_trigger_mode: bool) -> int:
        """设置触发模式"""
        device = None
        if isinstance(device_identifier, int):
            device = self.get_device_by_index(device_identifier)
        elif isinstance(device_identifier, str):
            device = self.get_device_by_serial(device_identifier)

        if device is None:
            return MV_E_HANDLE

        return device.set_trigger_mode(is_trigger_mode)

    def trigger_software(self, device_identifier) -> int:
        """软件触发"""
        device = None
        if isinstance(device_identifier, int):
            device = self.get_device_by_index(device_identifier)
        elif isinstance(device_identifier, str):
            device = self.get_device_by_serial(device_identifier)

        if device is None:
            return MV_E_HANDLE

        return device.trigger_software()

    def get_frame(
        self, device_identifier, timeout: int = 1000
    ) -> Optional[Dict[str, Any]]:
        """获取一帧图像"""
        device = None
        if isinstance(device_identifier, int):
            device = self.get_device_by_index(device_identifier)
        elif isinstance(device_identifier, str):
            device = self.get_device_by_serial(device_identifier)

        if device is None:
            return None

        return device.get_frame(timeout)

    def get_frame_bgr(
        self, device_identifier, timeout: int = 1000
    ) -> Optional[Dict[str, Any]]:
        """获取BGR8图像帧（使用内部转换）。"""
        device = None
        if isinstance(device_identifier, int):
            device = self.get_device_by_index(device_identifier)
        elif isinstance(device_identifier, str):
            device = self.get_device_by_serial(device_identifier)

        if device is None:
            return None

        return device.get_frame_bgr(timeout)

    def get_int_value(
        self, device_identifier, param_name: str
    ) -> Optional[Dict[str, Any]]:
        """获取整型参数值"""
        device = None
        if isinstance(device_identifier, int):
            device = self.get_device_by_index(device_identifier)
        elif isinstance(device_identifier, str):
            device = self.get_device_by_serial(device_identifier)

        if device is None:
            return None

        return device.get_int_value(param_name)

    def set_int_value(self, device_identifier, param_name: str, value: int) -> int:
        """设置整型参数值"""
        device = None
        if isinstance(device_identifier, int):
            device = self.get_device_by_index(device_identifier)
        elif isinstance(device_identifier, str):
            device = self.get_device_by_serial(device_identifier)

        if device is None:
            return MV_E_HANDLE

        return device.set_int_value(param_name, value)

    def get_float_value(
        self, device_identifier, param_name: str
    ) -> Optional[Dict[str, Any]]:
        """获取浮点型参数值"""
        device = None
        if isinstance(device_identifier, int):
            device = self.get_device_by_index(device_identifier)
        elif isinstance(device_identifier, str):
            device = self.get_device_by_serial(device_identifier)

        if device is None:
            return None

        return device.get_float_value(param_name)

    def set_float_value(self, device_identifier, param_name: str, value: float) -> int:
        """设置浮点型参数值"""
        device = None
        if isinstance(device_identifier, int):
            device = self.get_device_by_index(device_identifier)
        elif isinstance(device_identifier, str):
            device = self.get_device_by_serial(device_identifier)

        if device is None:
            return MV_E_HANDLE

        return device.set_float_value(param_name, value)

    def get_enum_value(
        self, device_identifier, param_name: str
    ) -> Optional[Dict[str, Any]]:
        """获取枚举型参数值"""
        device = None
        if isinstance(device_identifier, int):
            device = self.get_device_by_index(device_identifier)
        elif isinstance(device_identifier, str):
            device = self.get_device_by_serial(device_identifier)

        if device is None:
            return None

        return device.get_enum_value(param_name)

    def set_enum_value(self, device_identifier, param_name: str, value: int) -> int:
        """设置枚举型参数值"""
        device = None
        if isinstance(device_identifier, int):
            device = self.get_device_by_index(device_identifier)
        elif isinstance(device_identifier, str):
            device = self.get_device_by_serial(device_identifier)

        if device is None:
            return MV_E_HANDLE

        return device.set_enum_value(param_name, value)

    def get_bool_value(self, device_identifier, param_name: str) -> Optional[bool]:
        """获取布尔型参数值"""
        device = None
        if isinstance(device_identifier, int):
            device = self.get_device_by_index(device_identifier)
        elif isinstance(device_identifier, str):
            device = self.get_device_by_serial(device_identifier)

        if device is None:
            return None

        return device.get_bool_value(param_name)

    def set_bool_value(self, device_identifier, param_name: str, value: bool) -> int:
        """设置布尔型参数值"""
        device = None
        if isinstance(device_identifier, int):
            device = self.get_device_by_index(device_identifier)
        elif isinstance(device_identifier, str):
            device = self.get_device_by_serial(device_identifier)

        if device is None:
            return MV_E_HANDLE

        return device.set_bool_value(param_name, value)

    def get_string_value(self, device_identifier, param_name: str) -> Optional[str]:
        """获取字符串型参数值"""
        device = None
        if isinstance(device_identifier, int):
            device = self.get_device_by_index(device_identifier)
        elif isinstance(device_identifier, str):
            device = self.get_device_by_serial(device_identifier)

        if device is None:
            return None

        return device.get_string_value(param_name)

    def set_string_value(self, device_identifier, param_name: str, value: str) -> int:
        """设置字符串型参数值"""
        device = None
        if isinstance(device_identifier, int):
            device = self.get_device_by_index(device_identifier)
        elif isinstance(device_identifier, str):
            device = self.get_device_by_serial(device_identifier)

        if device is None:
            return MV_E_HANDLE

        return device.set_string_value(param_name, value)


# 使用示例
if __name__ == "__main__":
    # 创建相机管理器
    camera_manager = CameraManager()

    # 枚举设备
    ret = camera_manager.enum_devices()
    if ret != MV_OK:
        print(f"枚举设备失败, 错误码: 0x{ret:x}")
        exit()

    print(camera_manager.devices[0].serial_number)

    # device_count = camera_manager.get_device_count()
    # print(f"找到 {device_count} 个设备")
    #
    # if device_count == 0:
    #     print("没有找到设备")
    #     exit()

    # # 打开第一个设备
    # ret = camera_manager.open_device(0)
    # if ret != MV_OK:
    #     print(f"打开设备失败, 错误码: 0x{ret:x}")
    #     exit()
    #
    # # 设置触发模式为连续触发
    # camera_manager.set_trigger_mode(0, False)
    #
    # # 开始取流
    # ret = camera_manager.start_grabbing(0)
    # if ret != MV_OK:
    #     print(f"开始取流失败, 错误码: 0x{ret:x}")
    #     exit()
    #
    # # 获取几帧图像
    # for i in range(5):
    #     frame = camera_manager.get_frame(0)
    #     if frame:
    #         print(f"获取到第 {frame['frame_num']} 帧图像, "
    #               f"分辨率: {frame['width']}x{frame['height']}, "
    #               f"时间戳: {frame['timestamp']}")
    #     else:
    #         print("获取图像失败")
    #     time.sleep(0.1)
    #
    # # 停止取流
    # camera_manager.stop_grabbing(0)
    #
    # # 关闭设备
    # camera_manager.close_device(0)
    #
    # print("程序结束")
