{"system": {"name": "工业视觉缺陷检测系统", "version": "1.0.0", "language": "zh-CN"}, "camera": {"devices": [{"id": "CAMERA_001", "name": "前视相机", "enabled": true, "exposure_time": 10000, "gain": 10.0}]}, "detection": {"algorithms": [{"name": "YOLOv8_Defect_Detector", "type": "yolo", "model_path": "models/yolov8_defect.pt", "enabled": true}], "default_algorithm": "YOLOv8_Defect_Detector"}, "plc": {"connection": {"ip": "*************", "port": 502}}, "logging": {"level": "INFO", "file": {"path": "app.log"}}}